#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用所有数据的病毒分析图表
219种病毒 × 18个样本的完整数据展示
按照参考图样式制作
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import FancyBboxPatch, Circle, FancyArrowPatch
import matplotlib.patches as mpatches
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

def create_all_data_chart1():
    """使用所有219种病毒数据的参考图1样式"""
    print("创建使用所有219种病毒数据的图表...")
    
    # 读取完整数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')
    
    # 完整数据预处理
    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values
    
    print(f"完整数据: {len(viruses)} 种病毒, {len(sample_cols)} 个样本")
    print(f"数据矩阵大小: {abundance_matrix.shape}")
    print(f"总数据点: {abundance_matrix.size} 个")
    
    # 创建超大图表 - 1行3列
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 10))
    
    # === 子图a: 所有219种病毒的散点图 ===
    print("创建子图a: 所有219种病毒散点图...")
    
    # 计算每种病毒的统计特征
    virus_stats = []
    for i, virus in enumerate(viruses):
        virus_abundance = abundance_matrix[i, :]
        total_abundance = np.sum(virus_abundance)
        max_abundance = np.max(virus_abundance)
        detection_rate = np.sum(virus_abundance > 0) / len(sample_cols)  # 检出率
        cv = np.std(virus_abundance) / (np.mean(virus_abundance) + 1e-10)  # 变异系数
        
        virus_stats.append({
            'virus': virus,
            'total_abundance': total_abundance,
            'max_abundance': max_abundance,
            'detection_rate': detection_rate,
            'cv': cv
        })
    
    # 绘制所有219种病毒的散点图
    x_vals = [s['total_abundance'] for s in virus_stats]
    y_vals = [s['detection_rate'] for s in virus_stats]
    sizes = [s['max_abundance'] / 1000 + 10 for s in virus_stats]  # 大小反映最大丰度
    
    # 根据病毒类型着色
    colors = []
    for virus in viruses:
        virus_lower = virus.lower()
        if any(keyword in virus_lower for keyword in ['flavi', 'dengue', 'zika', 'yellow']):
            colors.append('#FF4444')  # 黄病毒科
        elif any(keyword in virus_lower for keyword in ['picorna', 'entero', 'polio']):
            colors.append('#44FF44')  # 小RNA病毒科
        elif any(keyword in virus_lower for keyword in ['rhabdo', 'vesicular']):
            colors.append('#4444FF')  # 弹状病毒科
        elif any(keyword in virus_lower for keyword in ['bunya', 'hanta', 'nairo']):
            colors.append('#FF44FF')  # 布尼亚病毒科
        elif any(keyword in virus_lower for keyword in ['orthomyxo', 'influenza']):
            colors.append('#FFFF44')  # 正粘病毒科
        elif any(keyword in virus_lower for keyword in ['circo', 'parvo']):
            colors.append('#44FFFF')  # 圆环病毒科
        else:
            colors.append('#888888')  # 其他
    
    scatter = ax1.scatter(x_vals, y_vals, s=sizes, c=colors, alpha=0.6, 
                         edgecolors='black', linewidth=0.5)
    
    # 标注高丰度病毒
    top_indices = np.argsort(x_vals)[-25:]  # 前25个高丰度病毒
    for idx in top_indices:
        virus_name = virus_stats[idx]['virus'].split()[0] if ' ' in virus_stats[idx]['virus'] else virus_stats[idx]['virus'][:10]
        ax1.annotate(virus_name, (x_vals[idx], y_vals[idx]), 
                    xytext=(5, 5), textcoords='offset points', 
                    fontsize=8, ha='left', alpha=0.8)
    
    ax1.set_xlabel('总丰度 Total Abundance (TPM)')
    ax1.set_ylabel('检出率 Detection Rate')
    ax1.set_title('a. 所有219种病毒分布分析\nAll 219 Viruses Distribution Analysis', 
                 fontsize=12, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log')
    
    # === 子图b: 所有18个样本的详细分析条形图 ===
    print("创建子图b: 所有18个样本分析...")
    
    # 计算每个样本的详细指标
    sample_indicators = []
    sample_names = []
    
    for i, sample in enumerate(sample_cols):
        sample_abundance = abundance_matrix[:, i]
        
        # 计算多个指标
        total_abundance = np.sum(sample_abundance)
        virus_count = np.sum(sample_abundance > 0)
        shannon_diversity = -np.sum((sample_abundance[sample_abundance > 0] / total_abundance) * 
                                   np.log(sample_abundance[sample_abundance > 0] / total_abundance + 1e-10))
        simpson_diversity = 1 - np.sum((sample_abundance / total_abundance) ** 2)
        evenness = shannon_diversity / np.log(virus_count + 1)
        dominance = np.max(sample_abundance) / total_abundance
        
        sample_indicators.append([
            total_abundance / 1000,  # 标准化
            virus_count,
            shannon_diversity,
            simpson_diversity * 100,
            evenness * 100,
            dominance * 100
        ])
        
        # 获取样本信息
        host_info = group_data[group_data['Unnamed: 0'] == sample]
        if not host_info.empty:
            host = host_info.iloc[0]['host']
            if 'Aedes aegypti' in host:
                sample_names.append(f'{sample}\n(埃及伊蚊)')
            elif 'Aedes albopictus' in host:
                sample_names.append(f'{sample}\n(白纹伊蚊)')
            elif 'Culex' in host:
                sample_names.append(f'{sample}\n(库蚊)')
            elif 'Rhipicephalus' in host:
                sample_names.append(f'{sample}\n(蜱虫)')
            elif 'sandfly' in host:
                sample_names.append(f'{sample}\n(白蛉)')
            elif 'Culicoides' in host:
                sample_names.append(f'{sample}\n(库蠓)')
            else:
                sample_names.append(f'{sample}\n(其他)')
        else:
            sample_names.append(f'{sample}\n(未知)')
    
    # 转换为数组
    sample_indicators = np.array(sample_indicators)
    
    # 绘制堆叠条形图
    indicator_names = ['总丰度(×1000)', '病毒种类数', '香农多样性', '辛普森多样性×100', '均匀度×100', '优势度×100']
    colors_indicators = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']
    
    x_pos = np.arange(len(sample_cols))
    bottom = np.zeros(len(sample_cols))
    
    for i, (indicator, color) in enumerate(zip(indicator_names, colors_indicators)):
        values = sample_indicators[:, i]
        ax2.bar(x_pos, values, bottom=bottom, label=indicator, color=color, alpha=0.8)
        bottom += values
    
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(sample_names, rotation=45, ha='right', fontsize=9)
    ax2.set_ylabel('指标值 Indicator Values')
    ax2.set_title('b. 所有18个样本多维度分析\nAll 18 Samples Multi-dimensional Analysis', 
                 fontsize=12, fontweight='bold')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # === 子图c: 完整的病毒-样本热力图 ===
    print("创建子图c: 完整病毒-样本热力图...")
    
    # 选择前25个高丰度病毒进行热力图展示
    total_abundance_per_virus = np.sum(abundance_matrix, axis=1)
    top_25_indices = np.argsort(total_abundance_per_virus)[-25:]
    top_25_viruses = [viruses[i] for i in top_25_indices]
    top_25_matrix = abundance_matrix[top_25_indices, :]
    
    # 对数转换
    log_matrix = np.log10(top_25_matrix + 1)

    # 绘制热力图
    im = ax3.imshow(log_matrix, cmap='Reds', aspect='auto', interpolation='nearest')

    # 设置坐标轴
    ax3.set_xticks(range(len(sample_cols)))
    ax3.set_yticks(range(0, len(top_25_viruses), 3))  # 每3个显示一个标签

    # 简化病毒名称
    virus_labels_short = []
    for i in range(0, len(top_25_viruses), 3):
        virus = top_25_viruses[i]
        if len(virus) > 25:
            virus_short = virus[:22] + '...'
        else:
            virus_short = virus
        virus_labels_short.append(virus_short)

    ax3.set_xticklabels(sample_cols, rotation=90, ha='center', fontsize=8)
    ax3.set_yticklabels(virus_labels_short, fontsize=7)

    ax3.set_title('c. 前25种高丰度病毒热力图\nTop 25 High-Abundance Viruses Heatmap',
                 fontsize=12, fontweight='bold')
    ax3.set_xlabel('样本 Samples')
    ax3.set_ylabel('病毒 Viruses')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax3, shrink=0.8)
    cbar.set_label('Log₁₀(TPM + 1)', rotation=270, labelpad=15, fontsize=10)
    
    # 设置总标题
    fig.suptitle('病毒组学完整数据分析 - 219种病毒 × 18个样本\n' +
                'Complete Virome Data Analysis - 219 Viruses × 18 Samples', 
                fontsize=18, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    plt.savefig('病毒丰度分布与样本多样性综合分析图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 病毒丰度分布与样本多样性综合分析图已保存: 病毒丰度分布与样本多样性综合分析图_2025.png")
    
    # 输出完整统计
    print(f"\n=== 完整数据统计 ===")
    print(f"病毒总数: {len(viruses)}")
    print(f"样本总数: {len(sample_cols)}")
    print(f"数据点总数: {abundance_matrix.size}")
    print(f"非零数据点: {np.sum(abundance_matrix > 0)}")
    print(f"数据覆盖率: {np.sum(abundance_matrix > 0) / abundance_matrix.size * 100:.1f}%")
    print(f"总丰度范围: {np.min(abundance_matrix[abundance_matrix > 0]):.1f} - {np.max(abundance_matrix):.1f} TPM")
    print(f"平均每样本病毒数: {np.mean([np.sum(abundance_matrix[:, i] > 0) for i in range(len(sample_cols))]):.1f}")
    print(f"平均每病毒检出样本数: {np.mean([np.sum(abundance_matrix[i, :] > 0) for i in range(len(viruses))]):.1f}")

def create_all_data_chart2():
    """使用所有数据的病毒传播网络图"""
    print("创建使用所有数据的病毒传播网络图...")

    # 读取完整数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')

    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values

    # 创建超大图
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_facecolor('#F5F5F5')

    # === 分析所有219种病毒的传播模式 ===
    print("分析所有219种病毒的传播模式...")

    # 计算病毒-宿主关联矩阵
    hosts = group_data['host'].unique()
    virus_host_matrix = np.zeros((len(viruses), len(hosts)))

    for host_idx, host in enumerate(hosts):
        host_samples = group_data[group_data['host'] == host]['Unnamed: 0'].tolist()
        matching_cols = [col for col in sample_cols if col in host_samples]

        if matching_cols:
            col_indices = [sample_cols.index(col) for col in matching_cols]
            host_abundance = np.mean(abundance_matrix[:, col_indices], axis=1)
            virus_host_matrix[:, host_idx] = host_abundance

    # 找出所有有意义的病毒-宿主关联
    significant_associations = []
    for virus_idx in range(len(viruses)):
        for host_idx in range(len(hosts)):
            if virus_host_matrix[virus_idx, host_idx] > 50:  # 阈值
                significant_associations.append({
                    'virus': viruses[virus_idx],
                    'virus_idx': virus_idx,
                    'host': hosts[host_idx],
                    'host_idx': host_idx,
                    'abundance': virus_host_matrix[virus_idx, host_idx]
                })

    print(f"发现 {len(significant_associations)} 个显著的病毒-宿主关联")

    # === 左侧：所有宿主类型 ===
    host_positions = {}
    host_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']

    y_positions = np.linspace(0.9, 0.1, len(hosts))

    for i, host in enumerate(hosts):
        # 简化宿主名称
        if 'Aedes aegypti' in host:
            host_name = '埃及伊蚊\nAedes aegypti'
            icon = '🦟'
        elif 'Aedes albopictus' in host:
            host_name = '白纹伊蚊\nAedes albopictus'
            icon = '🦟'
        elif 'Culex' in host:
            host_name = '库蚊\nCulex spp.'
            icon = '🦟'
        elif 'Rhipicephalus' in host:
            host_name = '蜱虫\nRhipicephalus spp.'
            icon = '🕷️'
        elif 'sandfly' in host:
            host_name = '白蛉\nSandfly'
            icon = '🪰'
        elif 'Culicoides' in host:
            host_name = '库蠓\nCulicoides spp.'
            icon = '🦟'
        else:
            host_name = host[:15]
            icon = '🐛'

        pos = (0.15, y_positions[i])
        host_positions[host] = pos

        # 计算该宿主的病毒统计
        host_viruses = [assoc for assoc in significant_associations if assoc['host'] == host]
        virus_count = len(host_viruses)
        total_abundance = sum([assoc['abundance'] for assoc in host_viruses])

        # 绘制宿主圆圈
        circle_size = min(0.06, max(0.03, virus_count / 50))  # 大小反映病毒数量
        circle = Circle(pos, circle_size, facecolor=host_colors[i % len(host_colors)],
                       edgecolor='black', alpha=0.8, linewidth=2)
        ax.add_patch(circle)

        # 宿主图标和信息
        ax.text(pos[0], pos[1], icon, ha='center', va='center', fontsize=16)
        ax.text(pos[0]-0.12, pos[1], host_name, ha='center', va='center',
               fontsize=10, fontweight='bold')
        ax.text(pos[0]-0.12, pos[1]-0.05, f'{virus_count}种病毒\n总丰度: {total_abundance:.0f}',
               ha='center', va='top', fontsize=8,
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    # === 右侧：病毒分类网络 ===
    print("创建病毒分类网络...")

    # 按病毒科分类
    virus_families = {
        '黄病毒科\nFlaviviridae': {'viruses': [], 'color': '#FF4444'},
        '小RNA病毒科\nPicornaviridae': {'viruses': [], 'color': '#44FF44'},
        '弹状病毒科\nRhabdoviridae': {'viruses': [], 'color': '#4444FF'},
        '布尼亚病毒科\nBunyaviridae': {'viruses': [], 'color': '#FF44FF'},
        '正粘病毒科\nOrthomyxoviridae': {'viruses': [], 'color': '#FFFF44'},
        '圆环病毒科\nCircoviridae': {'viruses': [], 'color': '#44FFFF'},
        '其他病毒科\nOthers': {'viruses': [], 'color': '#888888'}
    }

    # 分类所有病毒
    for virus in viruses:
        virus_lower = virus.lower()
        classified = False

        for family_key in virus_families.keys():
            family_lower = family_key.lower()
            if 'flavi' in family_lower and any(k in virus_lower for k in ['flavi', 'dengue', 'zika']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break
            elif 'picorna' in family_lower and any(k in virus_lower for k in ['picorna', 'entero']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break
            elif 'rhabdo' in family_lower and any(k in virus_lower for k in ['rhabdo', 'vesicular']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break
            elif 'bunya' in family_lower and any(k in virus_lower for k in ['bunya', 'hanta']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break
            elif 'orthomyxo' in family_lower and any(k in virus_lower for k in ['orthomyxo', 'influenza']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break
            elif 'circo' in family_lower and any(k in virus_lower for k in ['circo', 'parvo']):
                virus_families[family_key]['viruses'].append(virus)
                classified = True
                break

        if not classified:
            virus_families['其他病毒科\nOthers']['viruses'].append(virus)

    # 绘制病毒科
    family_positions = {}
    y_family_positions = np.linspace(0.9, 0.1, len(virus_families))

    for i, (family, data) in enumerate(virus_families.items()):
        if data['viruses']:  # 只显示有病毒的科
            pos = (0.85, y_family_positions[i])
            family_positions[family] = pos

            # 计算该科的统计
            family_virus_count = len(data['viruses'])
            family_associations = [assoc for assoc in significant_associations if assoc['virus'] in data['viruses']]
            family_total_abundance = sum([assoc['abundance'] for assoc in family_associations])

            # 绘制病毒科方块
            box_size = min(0.08, max(0.04, family_virus_count / 30))
            box = FancyBboxPatch((pos[0]-box_size/2, pos[1]-box_size/2), box_size, box_size,
                               boxstyle="round,pad=0.01",
                               facecolor=data['color'],
                               alpha=0.8, edgecolor='black', linewidth=2)
            ax.add_patch(box)

            # 病毒科标签
            ax.text(pos[0]+0.12, pos[1], family, ha='center', va='center',
                   fontsize=10, fontweight='bold')
            ax.text(pos[0]+0.12, pos[1]-0.05, f'{family_virus_count}种病毒\n总丰度: {family_total_abundance:.0f}',
                   ha='center', va='top', fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    # === 中间：传播连接线 ===
    print("绘制传播连接线...")

    # 绘制所有显著关联的连接线
    connection_count = 0
    for assoc in significant_associations:
        if connection_count > 100:  # 限制连接线数量避免过于密集
            break

        host = assoc['host']
        virus = assoc['virus']
        abundance = assoc['abundance']

        # 找到病毒所属的科
        virus_family = None
        for family, data in virus_families.items():
            if virus in data['viruses']:
                virus_family = family
                break

        if host in host_positions and virus_family in family_positions:
            from_pos = host_positions[host]
            to_pos = family_positions[virus_family]

            # 连接线粗细反映丰度
            linewidth = min(8, max(1, abundance / 1000))
            alpha = min(0.8, max(0.2, abundance / 5000))

            # 绘制连接线
            arrow = FancyArrowPatch(from_pos, to_pos,
                                   arrowstyle='->', mutation_scale=15,
                                   color=virus_families[virus_family]['color'],
                                   linewidth=linewidth, alpha=alpha)
            ax.add_patch(arrow)

            connection_count += 1

    # === 添加统计信息 ===
    stats_text = f"""完整数据统计 Complete Data Statistics:
• 病毒总数: {len(viruses)} 种
• 样本总数: {len(sample_cols)} 个
• 宿主类型: {len(hosts)} 种
• 显著关联: {len(significant_associations)} 个
• 连接线数: {min(connection_count, 100)} 条
• 数据覆盖率: {np.sum(abundance_matrix > 0) / abundance_matrix.size * 100:.1f}%"""

    ax.text(0.02, 0.02, stats_text, ha='left', va='bottom', fontsize=10,
           bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.9))

    # 设置坐标轴和标题
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')

    ax.text(0.5, 0.98, '病毒传播网络分析 - 基于所有219种病毒数据\nViral Transmission Network - Based on All 219 Viruses Data',
           ha='center', va='top', fontsize=18, fontweight='bold')

    plt.tight_layout()
    plt.savefig('病毒宿主关联模式与传播路径分析图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 病毒宿主关联模式与传播路径分析图已保存: 病毒宿主关联模式与传播路径分析图_2025.png")
    print(f"处理了 {len(significant_associations)} 个病毒-宿主关联")

if __name__ == "__main__":
    create_all_data_chart1()
    create_all_data_chart2()
