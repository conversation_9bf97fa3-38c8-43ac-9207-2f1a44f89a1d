#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
病毒-宿主关联网络图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import seaborn as sns
from matplotlib.patches import Circle
import matplotlib.patches as mpatches
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'Arial', 'DejaVu Sans']
plt.rcParams['font.size'] = 11
plt.rcParams['axes.unicode_minus'] = False

def create_virus_host_network():
    """创建病毒-宿主关联网络图"""
    print("创建病毒-宿主关联网络图...")
    
    # 读取数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')
    
    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values
    
    print(f"数据: {len(viruses)} 种病毒, {len(sample_cols)} 个样本")
    
    # 使用所有病毒物种
    all_viruses = viruses
    all_matrix = abundance_matrix

    print(f"使用所有{len(all_viruses)}种病毒进行网络分析")
    
    # 获取宿主信息
    hosts = group_data['host'].unique().tolist()
    print(f"宿主类型: {len(hosts)} 种")
    
    # 创建病毒-宿主关联矩阵
    virus_host_matrix = np.zeros((len(all_viruses), len(hosts)))

    for host_idx, host in enumerate(hosts):
        # 找到该宿主对应的样本
        host_samples = []
        for _, row in group_data.iterrows():
            if row['host'] == host and row['Unnamed: 0'] in sample_cols:
                host_samples.append(row['Unnamed: 0'])

        if host_samples:
            # 计算该宿主样本中病毒的平均丰度
            sample_indices = [sample_cols.index(sample) for sample in host_samples]
            host_abundance = all_matrix[:, sample_indices].mean(axis=1)
            virus_host_matrix[:, host_idx] = host_abundance
    
    # 标准化矩阵
    virus_host_matrix_norm = virus_host_matrix / (virus_host_matrix.max() + 1e-10)
    
    # 创建网络图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # === 左图：病毒-宿主关联热力图 ===
    print("绘制病毒-宿主关联热力图...")
    
    # 选择前30个高丰度病毒用于热力图显示
    total_abundance = np.sum(all_matrix, axis=1)
    top_30_indices = np.argsort(total_abundance)[-30:]
    top_30_viruses = [all_viruses[i] for i in top_30_indices]
    top_30_matrix = virus_host_matrix[top_30_indices, :]

    # 简化病毒名称
    virus_labels = []
    for virus in top_30_viruses:
        parts = virus.split()
        if len(parts) >= 2:
            genus = parts[0][:10]
            species = parts[1][:12] if len(parts[1]) < 12 else parts[1][:9] + '...'
            virus_labels.append(f"{genus}\n{species}")
        else:
            virus_labels.append(virus[:15] + '...' if len(virus) > 15 else virus)
    
    # 简化宿主名称
    host_labels = []
    for host in hosts:
        if 'Aedes aegypti' in host:
            host_labels.append('埃及伊蚊\nAe. aegypti')
        elif 'Aedes albopictus' in host:
            host_labels.append('白纹伊蚊\nAe. albopictus')
        elif 'Culex tritaeniorhymchus' in host:
            host_labels.append('三带喙库蚊\nCx. tritaeniorhymchus')
        elif 'Rhipicephalus microplus' in host:
            host_labels.append('微小牛蜱\nR. microplus')
        elif 'sandfly' in host:
            host_labels.append('白蛉\nSandfly')
        elif 'Culicoides' in host:
            host_labels.append('库蠓\nCulicoides')
        else:
            host_labels.append(host[:12])
    
    # 标准化热力图矩阵
    top_30_matrix_norm = top_30_matrix / (top_30_matrix.max() + 1e-10)

    # 绘制热力图
    im = ax1.imshow(top_30_matrix_norm, cmap='Reds', aspect='auto', interpolation='nearest')

    ax1.set_xticks(range(len(hosts)))
    ax1.set_yticks(range(30))
    ax1.set_xticklabels(host_labels, rotation=45, ha='right', fontsize=10)
    ax1.set_yticklabels(virus_labels, fontsize=8)

    ax1.set_title('病毒-宿主关联强度热力图\nVirus-Host Association Heatmap\n(前30种高丰度病毒)',
                 fontsize=14, fontweight='bold', pad=20)
    ax1.set_xlabel('宿主类型 Host Types', fontsize=12)
    ax1.set_ylabel('病毒 Viruses', fontsize=12)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax1, shrink=0.8)
    cbar.set_label('关联强度 Association Strength', fontsize=10)
    
    # === 右图：病毒-宿主网络图 ===
    print("绘制病毒-宿主网络图...")
    
    # 创建网络
    G = nx.Graph()
    
    # 设置关联阈值 - 降低阈值以显示更多连接
    threshold = 0.1  # 降低阈值以包含更多病毒

    # 添加节点 - 使用所有病毒
    virus_nodes = []
    host_nodes = []

    # 计算所有病毒的总丰度
    all_total_abundance = np.sum(all_matrix, axis=1)

    for i, virus in enumerate(all_viruses):
        virus_short = virus.split()[0] if ' ' in virus else virus[:12]
        virus_nodes.append(f"V_{virus_short}")
        G.add_node(f"V_{virus_short}", node_type='virus', abundance=all_total_abundance[i])

    for host in hosts:
        host_short = host.split()[0] if ' ' in host else host[:10]
        host_nodes.append(f"H_{host_short}")
        G.add_node(f"H_{host_short}", node_type='host')

    # 添加边 - 使用所有病毒的关联矩阵
    edges_added = 0
    for i in range(len(all_viruses)):
        for j in range(len(hosts)):
            if virus_host_matrix_norm[i, j] > threshold:
                virus_node = virus_nodes[i]
                host_node = host_nodes[j]
                weight = virus_host_matrix_norm[i, j]
                G.add_edge(virus_node, host_node, weight=weight)
                edges_added += 1
    
    print(f"网络节点: {len(G.nodes())}, 网络边: {len(G.edges())}")
    
    if len(G.nodes()) > 0 and len(G.edges()) > 0:
        # 设置布局
        pos = nx.spring_layout(G, k=3, iterations=100, seed=42)
        
        # 分离病毒和宿主节点
        virus_node_list = [node for node in G.nodes() if node.startswith('V_')]
        host_node_list = [node for node in G.nodes() if node.startswith('H_')]
        
        # 绘制病毒节点（红色圆形）
        virus_sizes = []
        for node in virus_node_list:
            abundance = G.nodes[node]['abundance']
            size = min(800, max(100, abundance / 2000))  # 调整大小范围
            virus_sizes.append(size)
        
        nx.draw_networkx_nodes(G, pos, nodelist=virus_node_list, 
                              node_color='#FF6B6B', node_size=virus_sizes, 
                              node_shape='o', alpha=0.8, ax=ax2)
        
        # 绘制宿主节点（蓝色三角形）
        nx.draw_networkx_nodes(G, pos, nodelist=host_node_list, 
                              node_color='#4ECDC4', node_size=1200, 
                              node_shape='^', alpha=0.8, ax=ax2)
        
        # 绘制边
        edges = G.edges(data=True)
        if edges:
            weights = [d['weight'] for u, v, d in edges]
            edge_widths = [w * 5 for w in weights]  # 根据权重调整线宽
            
            nx.draw_networkx_edges(G, pos, width=edge_widths,
                                  edge_color='gray', alpha=0.6, ax=ax2)
        
        # 添加标签
        labels = {}
        for node in G.nodes():
            if node.startswith('V_'):
                labels[node] = node[2:]  # 去掉 'V_' 前缀
            else:
                labels[node] = node[2:]  # 去掉 'H_' 前缀
        
        nx.draw_networkx_labels(G, pos, labels, font_size=9, ax=ax2)
        
        # 添加图例
        virus_patch = mpatches.Patch(color='#FF6B6B', label='病毒 Viruses')
        host_patch = mpatches.Patch(color='#4ECDC4', label='宿主 Hosts')
        edge_patch = mpatches.Patch(color='gray', label='关联关系 Associations')
        
        ax2.legend(handles=[virus_patch, host_patch, edge_patch],
                  loc='upper left', bbox_to_anchor=(0, 1), fontsize=11)
        
        ax2.set_title('病毒-宿主关联网络\nVirus-Host Association Network\n' +
                     f'(所有{len(all_viruses)}种病毒, 关联阈值 > {threshold}, {edges_added} 个连接)',
                     fontsize=14, fontweight='bold', pad=20)
    else:
        ax2.text(0.5, 0.5, '未发现显著的病毒-宿主关联\n(关联强度均低于阈值)', 
                ha='center', va='center', fontsize=14, transform=ax2.transAxes)
        ax2.set_title('病毒-宿主关联网络\nVirus-Host Association Network', 
                     fontsize=14, fontweight='bold', pad=20)
    
    ax2.axis('off')
    
    # 总标题
    fig.suptitle('病毒-宿主关联分析\nVirus-Host Association Analysis\n' +
                f'基于所有{len(all_viruses)}种病毒 (阈值: {threshold})',
                fontsize=16, fontweight='bold', y=0.95)

    # 添加统计信息
    stats_text = f"""数据统计 Statistics:
分析病毒数: {len(all_viruses)}
宿主类型数: {len(hosts)}
网络节点数: {len(G.nodes()) if len(G.nodes()) > 0 else 0}
网络连接数: {len(G.edges()) if len(G.edges()) > 0 else 0}
关联阈值: {threshold}"""
    
    fig.text(0.02, 0.15, stats_text, fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('病毒与宿主互作网络图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 病毒与宿主互作网络图已保存: 病毒与宿主互作网络图_2025.png")

    # 输出详细统计
    print(f"\n=== 网络分析结果 ===")
    print(f"分析的病毒数量: {len(all_viruses)}")
    print(f"宿主类型数量: {len(hosts)}")
    print(f"网络节点总数: {len(G.nodes()) if len(G.nodes()) > 0 else 0}")
    print(f"网络连接总数: {len(G.edges()) if len(G.edges()) > 0 else 0}")
    print(f"关联阈值设置: {threshold}")
    
    if len(G.edges()) > 0:
        print(f"\n=== 主要关联关系 ===")
        edge_weights = [(u, v, d['weight']) for u, v, d in G.edges(data=True)]
        edge_weights.sort(key=lambda x: x[2], reverse=True)
        
        for i, (u, v, weight) in enumerate(edge_weights[:10]):  # 显示前10个最强关联
            print(f"{i+1}. {u[2:]} ↔ {v[2:]}: {weight:.3f}")

if __name__ == "__main__":
    create_virus_host_network()
