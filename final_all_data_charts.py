#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新制作最终病毒分析图表 - 真正使用所有数据
219种病毒 × 18个样本的完整数据展示
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import FancyBboxPatch, Circle, FancyArrowPatch
import matplotlib.patches as mpatches
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

def create_final_all_data_chart():
    """重新制作最终病毒分析图表 - 使用所有数据"""
    print("重新制作最终病毒分析图表 - 使用所有219种病毒数据...")
    
    # 读取完整数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')
    
    # 完整数据预处理
    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values
    
    print(f"完整数据: {len(viruses)} 种病毒, {len(sample_cols)} 个样本")
    print(f"数据矩阵: {abundance_matrix.shape}")
    
    # 创建大图表 - 1行3列
    fig = plt.figure(figsize=(24, 10))
    
    # === 左图：病毒分类系统树 (使用更多病毒) ===
    ax1 = plt.subplot(1, 3, 1)
    print("创建病毒分类系统树 - 使用前30个高丰度病毒...")
    
    # 选择前30个高丰度病毒进行聚类
    total_abundance = np.sum(abundance_matrix, axis=1)
    top_indices = np.argsort(total_abundance)[-30:]  # 增加到30个
    top_viruses = [viruses[i] for i in top_indices]
    top_matrix = abundance_matrix[top_indices]
    
    # 层次聚类
    distances = pdist(top_matrix, metric='euclidean')
    linkage_matrix = linkage(distances, method='ward')
    
    # 创建更详细的病毒标签
    virus_labels = []
    for virus in top_viruses:
        parts = virus.split()
        if len(parts) >= 2:
            # 保留更多信息
            genus = parts[0][:8]
            species = parts[1][:12] if len(parts[1]) < 12 else parts[1][:9] + '...'
            virus_labels.append(f"{genus} {species}")
        else:
            virus_labels.append(virus[:18] + '...' if len(virus) > 18 else virus)
    
    # 绘制树状图
    dendrogram(linkage_matrix, 
               labels=virus_labels,
               orientation='left',
               leaf_font_size=8,
               color_threshold=0.7*max(linkage_matrix[:,2]))
    
    ax1.set_title('病毒分类系统树 (前30种高丰度病毒)\nVirus Classification Tree (Top 30)', 
                 fontsize=12, fontweight='bold')
    ax1.set_xlabel('聚类距离 Distance')
    ax1.grid(True, alpha=0.3)
    
    # === 中图：病毒-宿主关系热力图 (使用更多数据) ===
    ax2 = plt.subplot(1, 3, 2)
    print("创建病毒-宿主关系热力图 - 使用前30种病毒...")

    hosts = group_data['host'].unique().tolist()
    n_viruses = 30  # 增加显示的病毒数量
    
    # 选择高丰度病毒
    selected_indices = np.argsort(total_abundance)[-n_viruses:]
    selected_viruses = [viruses[i] for i in selected_indices]
    
    # 创建病毒-宿主丰度矩阵
    virus_host_matrix = np.zeros((n_viruses, len(hosts)))
    
    for host_idx, host in enumerate(hosts):
        host_samples = []
        for _, row in group_data.iterrows():
            if row['host'] == host and row['Unnamed: 0'] in sample_cols:
                host_samples.append(row['Unnamed: 0'])
        
        if host_samples:
            sample_indices = [sample_cols.index(sample) for sample in host_samples]
            for virus_idx in range(n_viruses):
                abundance = abundance_matrix[selected_indices[virus_idx], sample_indices].mean()
                virus_host_matrix[virus_idx, host_idx] = abundance
    
    # 对数转换
    log_matrix = np.log10(virus_host_matrix + 1)
    
    # 热力图
    im = ax2.imshow(log_matrix, cmap='Reds', aspect='auto')
    
    # 创建详细标签
    host_labels = []
    for host in hosts:
        if 'Aedes aegypti' in host:
            host_labels.append('埃及伊蚊')
        elif 'Aedes albopictus' in host:
            host_labels.append('白纹伊蚊')
        elif 'Culex' in host:
            host_labels.append('库蚊')
        elif 'Rhipicephalus' in host:
            host_labels.append('蜱虫')
        elif 'sandfly' in host:
            host_labels.append('白蛉')
        elif 'Culicoides' in host:
            host_labels.append('库蠓')
        else:
            host_labels.append(host[:8])
    
    virus_labels_heat = []
    for virus in selected_viruses:
        parts = virus.split()
        if len(parts) >= 2:
            genus = parts[0][:10]
            species = parts[1][:12] if len(parts[1]) < 12 else parts[1][:9] + '...'
            virus_labels_heat.append(f"{genus}\n{species}")
        else:
            virus_labels_heat.append(virus[:15] + '...' if len(virus) > 15 else virus)
    
    ax2.set_xticks(range(len(hosts)))
    ax2.set_yticks(range(n_viruses))
    ax2.set_xticklabels(host_labels, rotation=45, ha='right', fontsize=9)
    ax2.set_yticklabels(virus_labels_heat, fontsize=7)
    
    # 添加数值标注
    for i in range(n_viruses):
        for j in range(len(hosts)):
            if log_matrix[i, j] > 0.5:
                color = 'white' if log_matrix[i, j] > 2.5 else 'black'
                ax2.text(j, i, f'{log_matrix[i, j]:.1f}', 
                        ha='center', va='center', fontsize=7, 
                        color=color, fontweight='bold')
    
    ax2.set_title('病毒-宿主关系分析 (前25种病毒)\nVirus-Host Association (Top 25)', 
                 fontsize=12, fontweight='bold')
    ax2.set_xlabel('宿主类型')
    ax2.set_ylabel('病毒种类')
    
    # 颜色条
    cbar = plt.colorbar(im, ax=ax2, shrink=0.8)
    cbar.set_label('Log₁₀(TPM + 1)', rotation=270, labelpad=15)
    
    # === 右图：病毒流向图 (基于真实数据分析) ===
    ax3 = plt.subplot(1, 3, 3)
    print("创建病毒流向图 - 基于真实病毒分类...")
    
    # 基于真实数据进行病毒分类
    virus_categories = {
        'RNA病毒\n(多种科)': {'viruses': [], 'color': '#FF6B6B'},
        'DNA病毒\n(多种科)': {'viruses': [], 'color': '#4ECDC4'},
        '未分类病毒\n(新发现)': {'viruses': [], 'color': '#45B7D1'},
        '节肢动物病毒\n(特异性)': {'viruses': [], 'color': '#96CEB4'}
    }
    
    # 分析所有219种病毒进行分类
    for virus in viruses:
        virus_lower = virus.lower()
        classified = False
        
        # RNA病毒关键词
        rna_keywords = ['rna', 'flavi', 'picorna', 'rhabdo', 'bunya', 'orthomyxo', 'toga', 'corona']
        if any(keyword in virus_lower for keyword in rna_keywords):
            virus_categories['RNA病毒\n(多种科)']['viruses'].append(virus)
            classified = True
        
        # DNA病毒关键词
        elif any(keyword in virus_lower for keyword in ['dna', 'circo', 'parvo', 'adeno', 'herpes']):
            virus_categories['DNA病毒\n(多种科)']['viruses'].append(virus)
            classified = True
        
        # 节肢动物特异性病毒
        elif any(keyword in virus_lower for keyword in ['aedes', 'culex', 'tick', 'mosquito', 'mite']):
            virus_categories['节肢动物病毒\n(特异性)']['viruses'].append(virus)
            classified = True
        
        # 未分类或新发现病毒
        if not classified:
            virus_categories['未分类病毒\n(新发现)']['viruses'].append(virus)
    
    # 计算每个类别的真实统计数据
    for category, data in virus_categories.items():
        category_viruses = data['viruses']
        if category_viruses:
            # 计算该类别病毒的总丰度
            category_indices = [viruses.index(v) for v in category_viruses]
            category_abundance = np.sum(abundance_matrix[category_indices, :])
            data['total_abundance'] = category_abundance
            data['virus_count'] = len(category_viruses)
            data['avg_abundance'] = category_abundance / len(category_viruses)
        else:
            data['total_abundance'] = 0
            data['virus_count'] = 0
            data['avg_abundance'] = 0
    
    # 布局
    y_left = [0.8, 0.6, 0.4, 0.2]
    y_right = np.linspace(0.85, 0.15, len(hosts))
    
    # 左侧病毒类别 (基于真实数据大小)
    for i, (cat, data) in enumerate(virus_categories.items()):
        if data['virus_count'] > 0:
            # 框的大小反映病毒数量
            box_width = min(0.3, max(0.15, data['virus_count'] / 50))
            box_height = min(0.15, max(0.08, data['virus_count'] / 100))
            
            box = FancyBboxPatch((0.05, y_left[i]-box_height/2), box_width, box_height,
                               boxstyle="round,pad=0.01", 
                               facecolor=data['color'], 
                               alpha=0.8, linewidth=2)
            ax3.add_patch(box)
            
            ax3.text(0.05 + box_width/2, y_left[i], cat, 
                    ha='center', va='center', fontsize=9, 
                    fontweight='bold', color='white')
            
            ax3.text(0.05 + box_width/2, y_left[i]-0.04, 
                    f"{data['virus_count']}种\n总丰度:{data['total_abundance']:.0f}", 
                    ha='center', va='center', fontsize=7, 
                    color='white', style='italic')
    
    # 右侧宿主 (基于真实样本数据)
    colors_host = ['#FFB6C1', '#98FB98', '#87CEEB', '#DDA0DD', '#F0E68C', '#FFA07A']
    for i, host in enumerate(host_labels):
        # 计算该宿主的真实统计
        host_full = hosts[i]
        host_samples = group_data[group_data['host'] == host_full]['Unnamed: 0'].tolist()
        matching_cols = [col for col in sample_cols if col in host_samples]
        
        if matching_cols:
            col_indices = [sample_cols.index(col) for col in matching_cols]
            host_abundance = np.sum(abundance_matrix[:, col_indices])
            host_diversity = np.sum(np.sum(abundance_matrix[:, col_indices], axis=1) > 0)
        else:
            host_abundance = 0
            host_diversity = 0
        
        # 框的大小反映病毒多样性
        box_width = min(0.3, max(0.15, host_diversity / 50))
        box_height = min(0.1, max(0.06, host_diversity / 100))
        
        box = FancyBboxPatch((0.7, y_right[i]-box_height/2), box_width, box_height,
                           boxstyle="round,pad=0.01", 
                           facecolor=colors_host[i % len(colors_host)], 
                           alpha=0.8, linewidth=2)
        ax3.add_patch(box)
        
        ax3.text(0.7 + box_width/2, y_right[i], host, 
                ha='center', va='center', fontsize=9, fontweight='bold')
        
        ax3.text(0.7 + box_width/2, y_right[i]-0.025, 
                f"{len(matching_cols)}样本\n{host_diversity}种病毒", 
                ha='center', va='center', fontsize=7, style='italic')
    
    # 连接线 (基于真实关联强度)
    for i, (cat, data) in enumerate(virus_categories.items()):
        if data['virus_count'] > 0:
            for j, host in enumerate(hosts):
                # 计算真实的关联强度
                host_samples = group_data[group_data['host'] == host]['Unnamed: 0'].tolist()
                matching_cols = [col for col in sample_cols if col in host_samples]
                
                if matching_cols:
                    col_indices = [sample_cols.index(col) for col in matching_cols]
                    category_indices = [viruses.index(v) for v in data['viruses']]
                    
                    # 计算该类别病毒在该宿主中的总丰度
                    association_strength = np.sum(abundance_matrix[np.ix_(category_indices, col_indices)])
                    
                    if association_strength > 1000:  # 阈值
                        x1, y1 = 0.35, y_left[i]
                        x2, y2 = 0.7, y_right[j]
                        
                        # 线条粗细反映关联强度
                        linewidth = min(8, max(2, association_strength / 5000))
                        alpha = min(0.8, max(0.3, association_strength / 10000))
                        
                        # 贝塞尔曲线
                        t = np.linspace(0, 1, 50)
                        x = (1-t)**2 * x1 + 2*(1-t)*t * 0.525 + t**2 * x2
                        y = (1-t)**2 * y1 + 2*(1-t)*t * (y1+y2)/2 + t**2 * y2
                        
                        ax3.plot(x, y, color=data['color'], alpha=alpha, 
                                linewidth=linewidth, solid_capstyle='round')
    
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')
    ax3.set_title('病毒-宿主流向图 (基于真实数据)\nVirus-Host Flow (Real Data)', 
                 fontsize=12, fontweight='bold')
    
    # 图例
    legend_elements = []
    for cat, data in virus_categories.items():
        if data['virus_count'] > 0:
            legend_elements.append(mpatches.Patch(color=data['color'], 
                                                label=f"{cat.replace(chr(10), ' ')} ({data['virus_count']}种)"))
    ax3.legend(handles=legend_elements, loc='lower right', fontsize=8)
    
    # 总标题
    fig.suptitle('病毒-宿主关系综合分析 - 基于完整219种病毒数据\n' +
                'Comprehensive Virus-Host Analysis - Based on Complete 219 Viruses Data', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # 添加数据统计
    fig.text(0.02, 0.02, 
            f'数据统计: {len(viruses)}种病毒 | {len(sample_cols)}个样本 | ' +
            f'{np.sum(abundance_matrix > 0)}个检出数据点 | ' +
            f'覆盖率: {np.sum(abundance_matrix > 0)/abundance_matrix.size*100:.1f}%', 
            fontsize=10, style='italic', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('病毒系统发育与宿主特异性关联分析图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 病毒系统发育与宿主特异性关联分析图已保存: 病毒系统发育与宿主特异性关联分析图_2025.png")
    
    # 输出详细统计
    print(f"\n=== 完整数据使用统计 ===")
    print(f"病毒总数: {len(viruses)} (全部使用)")
    print(f"样本总数: {len(sample_cols)} (全部分析)")
    print(f"聚类分析病毒数: 30 (前30高丰度)")
    print(f"热力图显示病毒数: 25 (前25高丰度)")
    print(f"流向图病毒分类: 4大类 (覆盖全部219种)")
    
    for cat, data in virus_categories.items():
        print(f"  - {cat.replace(chr(10), ' ')}: {data['virus_count']}种病毒")

if __name__ == "__main__":
    create_final_all_data_chart()
