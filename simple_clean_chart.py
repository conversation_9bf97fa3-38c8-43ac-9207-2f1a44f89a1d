#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简洁清晰的病毒传播图 - 避免所有可能的显示问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Circle, Ellipse, Polygon
import matplotlib.patches as mpatches
import random
import warnings
warnings.filterwarnings('ignore')

# 使用最基础的字体设置
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12

def create_simple_clean_chart():
    """创建简洁清晰的病毒传播图"""
    print("Creating simple clean virus transmission chart...")
    
    # 读取数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')
    
    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values
    
    print(f"Data: {len(viruses)} viruses, {len(sample_cols)} samples")
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_facecolor('#F8F9FA')
    
    # 分析数据
    environments = group_data['Unnamed: 3'].dropna().unique().tolist()
    locations = group_data['location'].unique().tolist()
    
    # === 左侧：野生环境区域 ===
    wild_area = Ellipse((0.25, 0.5), 0.45, 0.85, 
                       facecolor='#E8F5E8', edgecolor='#2E7D32',
                       alpha=0.6, linewidth=3)
    ax.add_patch(wild_area)
    
    ax.text(0.25, 0.95, 'WILD ECOLOGICAL ENVIRONMENTS', 
           ha='center', va='center', fontsize=16, fontweight='bold', 
           color='#2E7D32')
    
    # 环境数据分析
    env_data = {}
    for env in environments:
        env_samples = group_data[group_data['Unnamed: 3'] == env]['Unnamed: 0'].tolist()
        matching_cols = [col for col in sample_cols if col in env_samples]
        
        if matching_cols:
            col_indices = [sample_cols.index(col) for col in matching_cols]
            env_abundance = np.sum(abundance_matrix[:, col_indices], axis=1)
            
            env_data[env] = {
                'sample_count': len(matching_cols),
                'virus_diversity': np.sum(env_abundance > 0),
                'total_abundance': np.sum(env_abundance)
            }
    
    # 环境信息映射
    env_mapping = {
        '山林': {'name': 'FOREST', 'pos': (0.15, 0.75), 'color': '#228B22'},
        '竹林': {'name': 'BAMBOO', 'pos': (0.35, 0.8), 'color': '#32CD32'},
        '畜舍': {'name': 'BARN', 'pos': (0.2, 0.6), 'color': '#8B4513'},
        '农田': {'name': 'FARMLAND', 'pos': (0.3, 0.45), 'color': '#DAA520'},
        '民房': {'name': 'RESIDENCE', 'pos': (0.18, 0.35), 'color': '#CD853F'},
        '草丛': {'name': 'GRASSLAND', 'pos': (0.32, 0.25), 'color': '#9ACD32'},
        '山林、草丛': {'name': 'MIXED HABITAT', 'pos': (0.12, 0.5), 'color': '#556B2F'},
        '竹林轮胎厂': {'name': 'INDUSTRIAL', 'pos': (0.38, 0.65), 'color': '#696969'}
    }
    
    # 绘制环境
    for env, data in env_data.items():
        if env in env_mapping:
            mapping = env_mapping[env]
            pos = mapping['pos']
            
            # 环境圆圈 - 大小反映病毒多样性
            circle_size = min(0.08, max(0.04, data['virus_diversity'] / 100))
            circle = Circle(pos, circle_size, 
                           facecolor=mapping['color'], 
                           edgecolor='black', alpha=0.8, linewidth=2)
            ax.add_patch(circle)
            
            # 环境名称
            ax.text(pos[0], pos[1]-circle_size-0.02, mapping['name'], 
                   ha='center', va='top', fontsize=10, fontweight='bold')
            
            # 统计信息
            stats_text = f"Samples: {data['sample_count']}\nViruses: {data['virus_diversity']}"
            ax.text(pos[0]+circle_size+0.05, pos[1], stats_text, 
                   ha='left', va='center', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", 
                            facecolor=mapping['color'], alpha=0.3))
    
    # === 右侧：人类活动区域 ===
    human_area = Ellipse((0.75, 0.5), 0.45, 0.85, 
                        facecolor='#FFF3E0', edgecolor='#E65100',
                        alpha=0.6, linewidth=3)
    ax.add_patch(human_area)
    
    ax.text(0.75, 0.95, 'HUMAN ACTIVITY AREAS', 
           ha='center', va='center', fontsize=16, fontweight='bold', 
           color='#E65100')
    
    # 地理位置数据分析
    location_data = {}
    for location in locations:
        loc_samples = group_data[group_data['location'] == location]['Unnamed: 0'].tolist()
        matching_cols = [col for col in sample_cols if col in loc_samples]
        
        if matching_cols:
            col_indices = [sample_cols.index(col) for col in matching_cols]
            loc_abundance = np.sum(abundance_matrix[:, col_indices], axis=1)
            virus_diversity = np.sum(loc_abundance > 0)
            
            # 风险等级
            if virus_diversity > 100:
                risk_level = 'VERY HIGH'
                risk_color = '#D32F2F'
            elif virus_diversity > 50:
                risk_level = 'HIGH'
                risk_color = '#F57C00'
            elif virus_diversity > 20:
                risk_level = 'MEDIUM'
                risk_color = '#FBC02D'
            else:
                risk_level = 'LOW'
                risk_color = '#388E3C'
            
            location_data[location] = {
                'sample_count': len(matching_cols),
                'virus_diversity': virus_diversity,
                'risk_level': risk_level,
                'risk_color': risk_color
            }
    
    # 地点信息映射
    location_mapping = {
        '瑞丽市': {'name': 'RUILI', 'pos': (0.85, 0.8)},
        '西盟县': {'name': 'XIMENG', 'pos': (0.65, 0.75)},
        '蒙自': {'name': 'MENGZI', 'pos': (0.8, 0.65)},
        '思茅': {'name': 'SIMAO', 'pos': (0.7, 0.55)},
        '普洱市景谷县钟山镇': {'name': 'ZHONGSHAN', 'pos': (0.85, 0.5)},
        '普洱市景谷县益智乡': {'name': 'YIZHI', 'pos': (0.65, 0.4)},
        '普洱市景谷县凤山镇': {'name': 'FENGSHAN', 'pos': (0.8, 0.35)},
        '玉溪市元江县': {'name': 'YUANJIANG', 'pos': (0.75, 0.25)},
        '勐海县': {'name': 'MENGHAI', 'pos': (0.6, 0.3)},
        '勐腊县': {'name': 'MENGLA', 'pos': (0.9, 0.2)},
        '昭通市绥江县': {'name': 'SUIJIANG', 'pos': (0.7, 0.15)}
    }
    
    # 绘制地点
    for location, data in location_data.items():
        if location in location_mapping:
            mapping = location_mapping[location]
            pos = mapping['pos']
            
            # 风险区域大小
            if data['risk_level'] == 'VERY HIGH':
                size = 0.08
            elif data['risk_level'] == 'HIGH':
                size = 0.06
            elif data['risk_level'] == 'MEDIUM':
                size = 0.05
            else:
                size = 0.04
            
            # 六边形区域
            angles = np.linspace(0, 2*np.pi, 7)
            x_coords = [pos[0] + size*np.cos(a) for a in angles]
            y_coords = [pos[1] + size*np.sin(a) for a in angles]
            
            polygon = Polygon(list(zip(x_coords, y_coords)), 
                            facecolor=data['risk_color'], alpha=0.7, 
                            edgecolor='black', linewidth=2)
            ax.add_patch(polygon)
            
            # 地点名称
            ax.text(pos[0], pos[1]-size-0.02, mapping['name'], 
                   ha='center', va='top', fontsize=10, fontweight='bold')
            
            # 风险信息
            risk_text = f"RISK: {data['risk_level']}\nVIRUSES: {data['virus_diversity']}"
            ax.text(pos[0]+size+0.05, pos[1], risk_text, 
                   ha='left', va='center', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", 
                            facecolor=data['risk_color'], alpha=0.3))
    
    # === 传播路径分析 ===
    print("Analyzing transmission pathways...")
    
    # 找跨环境病毒
    cross_env_viruses = []
    for i, virus in enumerate(viruses):
        virus_abundance = abundance_matrix[i, :]
        detected_samples = [sample_cols[j] for j, abundance in enumerate(virus_abundance) if abundance > 100]
        
        if len(detected_samples) >= 2:
            detected_envs = []
            detected_locs = []
            
            for sample in detected_samples:
                sample_info = group_data[group_data['Unnamed: 0'] == sample]
                if not sample_info.empty:
                    env = sample_info.iloc[0]['Unnamed: 3']
                    loc = sample_info.iloc[0]['location']
                    if pd.notna(env) and env in env_mapping:
                        detected_envs.append(env)
                    if pd.notna(loc) and loc in location_mapping:
                        detected_locs.append(loc)
            
            if detected_envs and detected_locs:
                cross_env_viruses.append({
                    'virus': virus,
                    'environments': detected_envs,
                    'locations': detected_locs,
                    'total_abundance': np.sum(virus_abundance)
                })
    
    print(f"Found {len(cross_env_viruses)} cross-environment viruses")
    
    # 绘制传播路径
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', 
              '#FF9FF3', '#F7DC6F', '#BB8FCE', '#85C1E9', '#82E0AA']
    
    # 按丰度排序，选择前25个跨环境病毒
    cross_env_viruses_sorted = sorted(cross_env_viruses,
                                     key=lambda x: x['total_abundance'], reverse=True)

    for i, virus_data in enumerate(cross_env_viruses_sorted[:25]):  # 显示前25个高丰度病毒
        virus_name = virus_data['virus'].split()[0] if ' ' in virus_data['virus'] else virus_data['virus'][:10]
        
        # 选择传播路径
        source_env = random.choice(virus_data['environments'])
        target_loc = random.choice(virus_data['locations'])
        
        if source_env in env_mapping and target_loc in location_mapping:
            from_pos = env_mapping[source_env]['pos']
            to_pos = location_mapping[target_loc]['pos']
            
            # 直线连接，避免复杂曲线
            linewidth = min(5, max(2, virus_data['total_abundance'] / 30000))
            alpha = 0.7
            
            ax.plot([from_pos[0], to_pos[0]], [from_pos[1], to_pos[1]], 
                   color=colors[i % len(colors)], linewidth=linewidth, alpha=alpha)
            
            # 箭头
            ax.annotate('', xy=to_pos, xytext=from_pos,
                       arrowprops=dict(arrowstyle='->', color=colors[i % len(colors)], 
                                     lw=linewidth, alpha=alpha))
            
            # 病毒标签
            mid_x = (from_pos[0] + to_pos[0]) / 2
            mid_y = (from_pos[1] + to_pos[1]) / 2
            
            ax.text(mid_x, mid_y, virus_name, 
                   ha='center', va='center', fontsize=9, fontweight='bold',
                   color=colors[i % len(colors)],
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', 
                            alpha=0.9, edgecolor=colors[i % len(colors)]))
    
    # === 图例和标题 ===
    # 风险等级图例
    risk_legend = [
        mpatches.Patch(color='#D32F2F', label='VERY HIGH RISK (>100 viruses)'),
        mpatches.Patch(color='#F57C00', label='HIGH RISK (50-100)'),
        mpatches.Patch(color='#FBC02D', label='MEDIUM RISK (20-50)'),
        mpatches.Patch(color='#388E3C', label='LOW RISK (<20)')
    ]
    
    ax.legend(handles=risk_legend, loc='lower center', 
             bbox_to_anchor=(0.5, 0.02), ncol=2, fontsize=11)
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # 主标题
    ax.text(0.5, 0.98, 'VIRAL ECOLOGICAL TRANSMISSION NETWORK', 
           ha='center', va='top', fontsize=20, fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))
    
    # 数据统计
    stats_text = f"""DATA SUMMARY:
Total Viruses: {len(viruses)}
Total Samples: {len(sample_cols)}
Environments: {len(environments)}
Locations: {len(locations)}
Cross-Environment Viruses: {len(cross_env_viruses)}"""
    
    ax.text(0.02, 0.2, stats_text, ha='left', va='top', fontsize=11,
           bbox=dict(boxstyle="round,pad=0.4", facecolor='lightyellow', alpha=0.9))
    
    plt.tight_layout()
    plt.savefig('病毒跨生态环境传播网络分析图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Virus cross-ecological transmission network chart saved: 病毒跨生态环境传播网络分析图_2025.png")

if __name__ == "__main__":
    create_simple_clean_chart()
