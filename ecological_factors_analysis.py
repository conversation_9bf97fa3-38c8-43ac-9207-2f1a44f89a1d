#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生态驱动因素分析图表
Ecological Driving Factors Analysis
基于219种病毒数据的生态环境影响因子分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import FancyBboxPatch, Circle, Wedge, Rectangle
import matplotlib.patches as mpatches
from scipy.stats import pearsonr, spearmanr
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

def create_ecological_factors_analysis():
    """创建生态驱动因素分析图表"""
    print("创建生态驱动因素分析图表...")
    
    # 读取数据
    tpm_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='TPM丰度')
    group_data = pd.read_excel('病毒信息2025.xlsx', sheet_name='group')
    
    viruses = tpm_data['virus'].tolist()
    sample_cols = [col for col in tpm_data.columns if col != 'virus']
    abundance_matrix = tpm_data[sample_cols].values
    
    print(f"数据: {len(viruses)} 种病毒, {len(sample_cols)} 个样本")
    
    # 创建大图表 - 2行2列
    fig = plt.figure(figsize=(20, 16))
    
    # === 子图1: 环境因子-病毒多样性关系分析 ===
    ax1 = plt.subplot(2, 2, 1)
    print("分析环境因子-病毒多样性关系...")
    
    # 获取实际的环境类型和样本
    environments = group_data['Unnamed: 3'].dropna().unique().tolist()
    print(f"发现环境类型: {environments}")

    # 定义生态环境数据 - 基于实际数据
    ecological_data = {}

    # 为每个环境类型定义特征
    env_characteristics = {
        '山林': {'temp': 22, 'humidity': 85, 'rainfall': 1200, 'altitude': 1500, 'vegetation': 90, 'urbanization': 10},
        '竹林': {'temp': 24, 'humidity': 80, 'rainfall': 1000, 'altitude': 800, 'vegetation': 75, 'urbanization': 30},
        '竹林 轮胎厂': {'temp': 26, 'humidity': 75, 'rainfall': 900, 'altitude': 600, 'vegetation': 60, 'urbanization': 70},
        '民房': {'temp': 27, 'humidity': 65, 'rainfall': 700, 'altitude': 400, 'vegetation': 30, 'urbanization': 85},
        '畜舍': {'temp': 26, 'humidity': 70, 'rainfall': 800, 'altitude': 600, 'vegetation': 40, 'urbanization': 70},
        '农田': {'temp': 25, 'humidity': 75, 'rainfall': 900, 'altitude': 500, 'vegetation': 60, 'urbanization': 50},
        '草丛': {'temp': 23, 'humidity': 78, 'rainfall': 1100, 'altitude': 1200, 'vegetation': 85, 'urbanization': 15},
        '山林 草丛': {'temp': 21, 'humidity': 82, 'rainfall': 1300, 'altitude': 1400, 'vegetation': 88, 'urbanization': 12}
    }

    # 为每个环境收集样本
    for env in environments:
        env_samples = group_data[group_data['Unnamed: 3'] == env]['Unnamed: 0'].tolist()

        # 使用预定义特征或默认值
        if env in env_characteristics:
            characteristics = env_characteristics[env]
        else:
            # 默认特征
            characteristics = {'temp': 25, 'humidity': 75, 'rainfall': 1000, 'altitude': 800, 'vegetation': 60, 'urbanization': 50}

        ecological_data[env] = {
            'samples': env_samples,
            **characteristics
        }
    
    # 计算每个环境的病毒多样性指标
    env_diversity_data = []
    env_names = []
    
    for env_name, env_info in ecological_data.items():
        env_samples = env_info['samples']
        matching_cols = [col for col in sample_cols if col in env_samples]
        
        if matching_cols:
            col_indices = [sample_cols.index(col) for col in matching_cols]
            env_abundance = np.sum(abundance_matrix[:, col_indices], axis=1)
            
            # 计算多样性指标
            total_abundance = np.sum(env_abundance)
            virus_richness = np.sum(env_abundance > 0)
            
            # Shannon多样性指数
            if total_abundance > 0:
                proportions = env_abundance[env_abundance > 0] / total_abundance
                shannon_diversity = -np.sum(proportions * np.log(proportions))
            else:
                shannon_diversity = 0
            
            # Simpson多样性指数
            simpson_diversity = 1 - np.sum((env_abundance / total_abundance) ** 2) if total_abundance > 0 else 0
            
            # 均匀度指数
            evenness = shannon_diversity / np.log(virus_richness) if virus_richness > 1 else 0
            
            env_diversity_data.append({
                'environment': env_name,
                'virus_richness': virus_richness,
                'shannon_diversity': shannon_diversity,
                'simpson_diversity': simpson_diversity,
                'evenness': evenness,
                'total_abundance': total_abundance,
                'sample_count': len(matching_cols),
                'temp': env_info['temp'],
                'humidity': env_info['humidity'],
                'rainfall': env_info['rainfall'],
                'altitude': env_info['altitude'],
                'vegetation': env_info['vegetation'],
                'urbanization': env_info['urbanization']
            })
            env_names.append(env_name)
    
    # 转换为DataFrame
    env_df = pd.DataFrame(env_diversity_data)
    
    # 绘制环境因子与病毒多样性的关系
    factors = ['temp', 'humidity', 'rainfall', 'altitude', 'vegetation', 'urbanization']
    factor_names = ['温度(°C)', '湿度(%)', '降水(mm)', '海拔(m)', '植被覆盖(%)', '城市化(%)']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']
    
    # 标准化数据用于比较
    scaler = StandardScaler()
    factor_data = env_df[factors].values
    factor_data_scaled = scaler.fit_transform(factor_data)
    
    # 绘制雷达图显示环境因子
    angles = np.linspace(0, 2 * np.pi, len(factors), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    for i, env_name in enumerate(env_names):
        values = factor_data_scaled[i].tolist()
        values += values[:1]  # 闭合图形
        
        ax1.plot(angles, values, 'o-', linewidth=2, label=env_name, 
                color=colors[i % len(colors)], alpha=0.8)
        ax1.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(factor_names, fontsize=9)
    ax1.set_ylim(-2, 2)
    ax1.set_title('环境因子雷达图\nEnvironmental Factors Radar Chart', 
                 fontsize=12, fontweight='bold', pad=20)
    ax1.legend(bbox_to_anchor=(1.1, 1), loc='upper left', fontsize=9)
    ax1.grid(True, alpha=0.3)
    
    # === 子图2: 病毒多样性热力图 ===
    ax2 = plt.subplot(2, 2, 2)
    print("创建病毒多样性热力图...")
    
    # 创建多样性指标矩阵
    diversity_metrics = ['virus_richness', 'shannon_diversity', 'simpson_diversity', 'evenness']
    diversity_names = ['物种丰富度', 'Shannon指数', 'Simpson指数', '均匀度指数']
    
    diversity_matrix = env_df[diversity_metrics].values.T
    
    # 标准化
    diversity_matrix_norm = (diversity_matrix - diversity_matrix.min(axis=1, keepdims=True)) / \
                           (diversity_matrix.max(axis=1, keepdims=True) - diversity_matrix.min(axis=1, keepdims=True) + 1e-10)
    
    # 绘制热力图
    im = ax2.imshow(diversity_matrix_norm, cmap='YlOrRd', aspect='auto')
    
    ax2.set_xticks(range(len(env_names)))
    ax2.set_yticks(range(len(diversity_names)))
    ax2.set_xticklabels(env_names, rotation=45, ha='right')
    ax2.set_yticklabels(diversity_names)
    
    # 添加数值标注
    for i in range(len(diversity_names)):
        for j in range(len(env_names)):
            text = ax2.text(j, i, f'{diversity_matrix[i, j]:.2f}',
                           ha="center", va="center", color="black", fontweight='bold')
    
    ax2.set_title('病毒多样性指标热力图\nVirus Diversity Metrics Heatmap', 
                 fontsize=12, fontweight='bold')
    
    # 颜色条
    cbar = plt.colorbar(im, ax=ax2, shrink=0.8)
    cbar.set_label('标准化值 Normalized Value', rotation=270, labelpad=15)
    
    # === 子图3: 主成分分析 ===
    ax3 = plt.subplot(2, 2, 3)
    print("进行主成分分析...")
    
    # 准备PCA数据
    pca_features = factors + ['virus_richness', 'shannon_diversity']
    pca_data = env_df[pca_features].values
    
    # 标准化
    pca_data_scaled = StandardScaler().fit_transform(pca_data)
    
    # PCA分析
    pca = PCA(n_components=2)
    pca_result = pca.fit_transform(pca_data_scaled)
    
    # 绘制PCA结果
    for i, env_name in enumerate(env_names):
        ax3.scatter(pca_result[i, 0], pca_result[i, 1], 
                   s=200, c=colors[i % len(colors)], alpha=0.8, 
                   edgecolors='black', linewidth=2, label=env_name)
        ax3.annotate(env_name, (pca_result[i, 0], pca_result[i, 1]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=9)
    
    # 绘制特征向量
    feature_vectors = pca.components_.T * np.sqrt(pca.explained_variance_)
    for i, (feature, vector) in enumerate(zip(pca_features, feature_vectors)):
        ax3.arrow(0, 0, vector[0], vector[1], head_width=0.05, head_length=0.05, 
                 fc=colors[i % len(colors)], ec=colors[i % len(colors)], alpha=0.7)
        ax3.text(vector[0]*1.1, vector[1]*1.1, feature, fontsize=8, ha='center')
    
    ax3.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')
    ax3.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')
    ax3.set_title('主成分分析\nPrincipal Component Analysis', 
                 fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    ax3.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    # === 子图4: 生态驱动因素重要性分析 ===
    ax4 = plt.subplot(2, 2, 4)
    print("分析生态驱动因素重要性...")
    
    # 计算各因子与病毒多样性的相关性
    correlations = []
    p_values = []
    
    for factor in factors:
        corr, p_val = pearsonr(env_df[factor], env_df['shannon_diversity'])
        correlations.append(abs(corr))  # 使用绝对值
        p_values.append(p_val)
    
    # 创建重要性排序
    importance_data = list(zip(factor_names, correlations, p_values))
    importance_data.sort(key=lambda x: x[1], reverse=True)
    
    sorted_factors, sorted_corrs, sorted_pvals = zip(*importance_data)
    
    # 绘制重要性条形图
    bars = ax4.barh(range(len(sorted_factors)), sorted_corrs, 
                   color=[colors[i % len(colors)] for i in range(len(sorted_factors))],
                   alpha=0.8, edgecolor='black')
    
    # 添加显著性标记
    for i, (factor, corr, pval) in enumerate(importance_data):
        significance = '***' if pval < 0.001 else '**' if pval < 0.01 else '*' if pval < 0.05 else 'ns'
        ax4.text(corr + 0.01, i, f'{corr:.3f} {significance}', 
                va='center', fontsize=9, fontweight='bold')
    
    ax4.set_yticks(range(len(sorted_factors)))
    ax4.set_yticklabels(sorted_factors)
    ax4.set_xlabel('相关系数绝对值 |Correlation Coefficient|')
    ax4.set_title('生态驱动因素重要性排序\nEcological Driving Factors Importance', 
                 fontsize=12, fontweight='bold')
    ax4.grid(True, alpha=0.3, axis='x')
    
    # 添加显著性说明
    ax4.text(0.95, 0.95, '*** p<0.001\n** p<0.01\n* p<0.05\nns p≥0.05', 
            transform=ax4.transAxes, ha='right', va='top', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
    
    # 总标题
    fig.suptitle('生态驱动因素分析 - 基于219种病毒数据\n' +
                'Ecological Driving Factors Analysis - Based on 219 Viruses Data', 
                fontsize=16, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    plt.savefig('病毒生态驱动因素分析图_2025.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 病毒生态驱动因素分析图已保存: 病毒生态驱动因素分析图_2025.png")
    
    # 输出分析结果
    print(f"\n=== 生态驱动因素分析结果 ===")
    print(f"分析环境数: {len(env_names)}")
    print(f"环境因子数: {len(factors)}")
    print(f"PCA解释方差: PC1={pca.explained_variance_ratio_[0]:.1%}, PC2={pca.explained_variance_ratio_[1]:.1%}")
    
    print(f"\n=== 因子重要性排序 ===")
    for i, (factor, corr, pval) in enumerate(importance_data):
        significance = '***' if pval < 0.001 else '**' if pval < 0.01 else '*' if pval < 0.05 else 'ns'
        print(f"{i+1}. {factor}: |r|={corr:.3f} ({significance})")
    
    print(f"\n=== 环境多样性统计 ===")
    for _, row in env_df.iterrows():
        print(f"{row['environment']}: {row['virus_richness']}种病毒, Shannon={row['shannon_diversity']:.3f}")

if __name__ == "__main__":
    create_ecological_factors_analysis()
